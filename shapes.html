<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>

    <canvas id="myCanvas" width="1500" height="1000"></canvas>

    <script>
        const canvas = document.getElementById("myCanvas");
        const ctx = canvas.getContext("2d")
        
        //drawing a rectangle
        ctx.fillStyle = "#0000FF"
        ctx.fillRect(300, 200, 300, 300);

        //drawing a triangle
        // ctx.fillStyle = "#0000FF"
        // ctx.beginPath()
        // ctx.moveTo(1100,200);
        // ctx.lineTo(1400,350);
        // ctx.lineTo(1100,500);
        // ctx.fill();

        //drawing a stroke
        ctx.strokeStyle = "#0000FF"
        ctx.lineWidth = 20;
        ctx.beginPath();
        ctx.moveTo(700,200);
        ctx.lineTo(1000,350);
        ctx.lineTo(700,500);
        ctx.stroke();

        //drawing an arc
        ctx.strokeStyle = "#0000FF";
        ctx.lineWidth = 30;

        ctx.beginPath();
        ctx.moveTo(300,700);
        ctx.quadraticCurveTo(400,1000,1000,700);
        ctx.stroke();
    </script>
</body>
</html>