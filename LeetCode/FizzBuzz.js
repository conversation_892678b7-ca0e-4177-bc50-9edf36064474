var fizzBuzz = function(n) {
    answer = []
    for (let i = 1; i <= n; i++) {
        answer.push(String(i));
    }
    for (let i = 0; i < answer.length; i++) {
        if ((i+1)%3 == 0 && (i+1)%5 == 0) {
            answer[i] = "FizzBuzz"
        }
        if ((i+1)%3 == 0 && (i+1)%5 != 0) {
            answer[i] = "Fizz"
        }
        if (((i+1)%3 != 0 && (i+1)%5 == 0)) {
            answer[i] = "Buzz"
        }
    }
    return answer;
}

console.log(fizzBuzz(5))
console.log(fizzBuzz(10))
console.log(fizzBuzz(15))