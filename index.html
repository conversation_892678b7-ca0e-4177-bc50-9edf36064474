<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome!</title>
    <style>
    body {
        font-family: 'Comic Sans MS', sans-serif; 
        display: flex; 
        flex-direction: column; 
        align-items: center; 
        justify-content: center; 
        height: 100vh; 
        text-align: center; 
    }
    button {
        padding: 12px 24px; 
        font-size: 18px; 
        cursor: pointer; 
        border: none; 
        border-radius: 4px; 
        background: #3498db; 
        color: white;
        margin-bottom: 20px;
    }
    button:hover {
        background: #2980b9;
    }
    p {
        font-size: 34px; 
        margin-bottom: 20px;
    }
    </style>
</head>
<body>
    <p>Welcome to my app! Click any button below to go to any app</p>
    <button onclick="location.href='ExpenseTracker/expense.html'">Go to Expense Tracker</button>
    <button onclick="location.href='Calculator/calculator.html'">Go to Calculator</button>
    <button onclick="location.href='Stopwatch/stopwatch.html'">Go to Stopwatch</button>
    <button onclick="location.href='DoodleJump/doodlejump.html'">Go to Doodle Jump</button>
    <button onclick="location.href='2048/2048.html'">Go to 2048</button>
    <button onclick="location.href='Platformer/platformer.html'">Go to Platformer</button>
</body>
</html>
