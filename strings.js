let text = "Apple, Banana, <PERSON><PERSON>"
let part = text.slice(7,13)
let part2 = text.slice(-12)
let part3 = text.slice(0,5)

let text2 = "Hello"
let upper = text2.toUpperCase()
let lower = upper.toLowerCase()

console.log(`${part} \n ${part2} \n ${part3} \n ${upper} \n ${lower} \n ${text2.repeat(5)}`)

function replaceText() {
    let text4 = document.getElementById("demo2").innerHTML;
    console.log(text4.replace("Hello World", "HELLO WORLD"))
    document.getElementById("demo2").innerHTML = text4.replace("Hello World", "HELLO WORLD")
}