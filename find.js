var reverseVowels = function(s) {
    //two pointers
    let word = Array.from(s)
    vowels = "aeiouAEIOU"
    let start = 0 
    let end = s.length - 1

    while (start < end) {
        while (start < end && vowels.includes(word[start]) == -1) {
            start += 1
        }
        while (start < end && vowels.includes(word[end]) == -1) {
            end -= 1
        }
        word[start], word[end] = word[end], word[start]

        start += 1
        end -= 1
    }
    return "".concat(word)
}

console.log(reverseVowels("leetcode"))