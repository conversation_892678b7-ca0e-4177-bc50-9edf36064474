
//Making the constructor function for the Person object
function Person(first, last, age, eyecolor) {
    this.firstName = first;
    this.lastName = last;
    this.age = age;
    this.eyeColor = eyecolor;
    this.fullName = function(){
        return this.firstName + " " + this.lastName
    };
}
//creating a person object
const myFather = new Person("<PERSON>", "<PERSON><PERSON>", 50, "blue");

//displaying the full name
text = ""
text += "My father is " + myFather.fullName()
console.log(text)