var myGlobal = 10;
function fun1() {
	//if we use the var keyword then it will be set as a local variable
	//without writing var it will be automatically set as a global variable
	oopsGlobal = 5;
}

function fun2() {
	var output = ""
	if (typeof myGlobal != "undefined") {
		output += "myGlobal: " + myGlobal;
	}
	if (typeof oopsGlobal != "undefined") {
		output += " oopsGlobal: " + oopsGlobal
	}
	console.log(output)
}
fun1()
fun2()