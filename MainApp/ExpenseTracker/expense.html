<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Expense Tracker</title>
  <link rel="stylesheet" href="expense.css">
</head>
<body>
  <h1>EXPENSE TRACKER</h1>

  <div class="input-group">
    <label for="name">Expense Name:</label>
    <input type="text" id="name" />
  </div>
  <div class="input-group">
    <label for="amount">Expense Value:</label>
    <input type="number" id="amount" min="0" />
  </div>
  <div class="input-group">
    <label for="date">Date:</label>
    <input type="date" id="date" />
  </div>
  <div class="input-group">
    <label>Category:</label>
    <select id="category">
      <option>Food</option>
      <option>Transport</option>
      <option>Entertainment</option>
      <option>Utilities</option>
      <option>Other</option>
    </select>
  </div>

  <div class="actions">
    <button id="addBtn">Add Expense</button>
    <button id="removeBtn">Remove Selected</button>
    <button id="saveBtn">Save Expenses</button>
    <button id="clearBtn" class="danger">Clear All</button>
  </div>

  <table id="expenseTable">
    <thead>
      <tr>
        <th>Name</th>
        <th>Amount</th>
        <th>Date</th>
        <th>Category</th>
      </tr>
    </thead>
    <tbody></tbody>
  </table>

  <script src="expense.js"></script>
</body>
</html>