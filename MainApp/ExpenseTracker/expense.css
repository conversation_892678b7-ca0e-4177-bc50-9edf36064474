body {
      font-family: 'Comic Sans MS', sans-serif;
      max-width: 900px;
      margin: 40px auto;
      padding: 0 20px;
    }
    h1 {
      font-size: 32px;
      text-align: center;
      margin-bottom: 30px;
    }
    .input-group {
      margin-bottom: 15px;
    }
    .input-group label {
      display: inline-block;
      width: 120px;
      font-size: 18px;
    }
    .input-group input,
    .input-group select {
      padding: 6px;
      font-size: 16px;
      width: 200px;
    }
    .actions {
      margin: 20px 0;
      text-align: center;
    }
    .actions button {
      font-size: 14px;
      padding: 8px 16px;
      margin: 0 10px;
      cursor: pointer;
      border: none;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .actions .danger {
      background-color: #e74c3c;
      color: white;
    }
    .actions button:not(.danger) {
      background-color: #3498db;
      color: white;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }
    table, th, td {
      border: 1px solid #ccc;
    }
    th, td {
      padding: 10px;
      text-align: left;
    }
    th {
      background: #f0f0f0;
    }
    tr.selected {
      background: #d1ecf1;
    }

    .home button {
      position: fixed;
      top: 10px;
      right: 10px;
      font-size: 14px;
      padding: 8px 16px;
      cursor: pointer;
      border: none;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      background-color: #3498db;
    }