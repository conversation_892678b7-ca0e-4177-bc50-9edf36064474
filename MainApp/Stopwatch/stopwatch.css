body{
    display: flex;
    flex-direction: column;
    align-items: center;
    background: hsl(0, 0%, 90%);
}

#myH1{
    font-size: 4rem;
    font-family: "Arial", sans-serif;
    color: hsl(0, 0%, 25%);
}

#container{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30px;
    border: 4px solid;
    border-radius: 50px;
    background-color: white;
}

#display{
    font-size: 5rem;
    font-family: monospace;
    font-weight: bold;
    color: hsl(0, 0%, 30%);
    text-shadow: 2px 2px 2px hsla(0, 0%, 0%, 0.75);
    margin-bottom: 24px;
}

#controls button{
    font-size: 1.5rem;
    font-weight: bold;
    padding: 10px 20px;
    margin: 4px;
    min-width: 124px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    color: white;
    transition: background-color 0.5s ease;
}

#startBtn {
    background-color: hsl(110, 63%, 40%);
}

#startBtn:hover {
    background-color: hsl(110, 63%, 30%);
}

#stopBtn {
    background-color: hsl(0, 80%, 50%);
}

#stopBtn:hover {
    background-color: hsl(0, 80%, 40%);
}

#resetBtn {
    background-color: hsl(236, 100%, 50%);
}

#resetBtn:hover {
    background-color: hsl(236, 100%, 40%);
}

.home button {
    position: fixed;
    top: 10px;
    right: 10px;
    font-size: 14px;
    padding: 8px 16px;
    cursor: pointer;
    border: none;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    background-color: #3498db;
}