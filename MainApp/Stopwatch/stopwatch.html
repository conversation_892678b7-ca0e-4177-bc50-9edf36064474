<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stopwatch</title>
    <link rel="stylesheet" href="stopwatch.css">
</head>
<body>
    <h1 id="myH1">Stopwatch</h1>

    <div id="container">
        <div id="display">
            00:00:00:00
        </div>

        <div id="controls">
            <button id="startBtn" onclick="start()">Start</button>
            <button id="stopBtn" onclick = "stop()">Stop</button>
            <button id="resetBtn" onclick = "reset()">Reset</button>
        </div>
    </div>

    <div class="home">
        <button onclick="location.href='../index.html'">Back to Home</button>
    </div>

    <script src="stopwatch.js"></script>
</body>
</html>