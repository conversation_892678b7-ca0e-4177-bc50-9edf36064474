body{
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: hsl(0,0%,95%);
}

#calculator{
    font-family: Arial, sans-serif;
    background-color: hsl(0,0%,15%);
    border-radius: 15px;
    max-width: 500px;
    overflow: hidden;
    border: rgba(0, 0, 0) 15px inset;
}

#display{
    width: 100%;
    padding: 20px;
    font-size: 5rem;
    text-align: left;
    border: none;
    background-color: hsl(0,0%, 25%);
    color: white;
}

#keys{
    display: grid;
    grid-template-columns: repeat(4,1fr);
    gap: 10px;
    padding: 25px;
}

button{
    width: 100px;
    height: 100px;
    border-radius: 50px;
    border: none;
    background-color: hsl(0,0%,30%);
    color: white;
    font-size: 3rem;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.5s ease;
}

button:hover{
    background-color: hsl(0,0%,40%);
}

button:active{
    background-color: hsl(0,0%,50%);
}

.operator-btn{
    background-color: hsl(39,100%,55%);
}

.operator-btn:hover{
    background-color: hsl(39,100%,65%);
}

.operator-btn:active{
    background-color: hsl(39,100%,75%);
}

.ans-btn{
    font-size : 2rem;
    background-color: hsl(0, 0%, 40%);
}

.ans-btn:hover{
    background-color: hsl(0, 0%, 50%);
}