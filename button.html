<!DOCTYPE html>
<html>

<body>

<button onclick= "blue()" class = "blue">Blue button</button>
<p id = "blue"></p>
<button onclick= "green()" class = "green">Green button</button>
<p id = "green"></p>
<button onclick= "red()" class = "red">Red button</button>
<p id = "red"></p>
<script>
    function blue() {
        document.getElementById("blue").innerHTML = "You clicked the blue button"
    }
    function green() {
        document.getElementById("green").innerHTML = "You clicked the green button"
    }
    function red() {
        document.getElementById("red").innerHTML = "You clicked the red button"
    }

</script>

<style>

    .blue {
        background-color: rgb(0, 0, 255);
        text-decoration-color: black;
        border: 15;
        border-color: rgb(0, 0, 0);
        padding: 10px 20px;
        font-size: 15px;
    }
    .green {
        background-color: rgb(0,255,0);
        text-decoration-color: black;
        border: 15;
        border-color: rgb(0,0,0);
        padding: 10px 20px;
        font-size: 15px;
    }
    .red {
        background-color: rgb(255,0,0);
        text-decoration-color: black;
        border: 15;
        border-color: rgb(0,0,0);
        padding: 10px 20px;
        font-size: 15px;
    }
</style>

</body>
</html>